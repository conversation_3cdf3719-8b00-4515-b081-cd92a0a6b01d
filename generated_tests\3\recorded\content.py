from playwright.async_api import async_playwright
import asyncio

async def test_recorded_actions():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        await page.goto('https://test.teamstreamz.com/')
        await page.click('#Username')
        await page.fill('#Username', '<EMAIL>')
        await page.fill('#Password', 'Test@123')
        await page.click('.btn.signin-btn.bg-primary.ng-tns-c813547932-0')
        await page.click('#Password')
        await page.fill('#Password', 'Test@123')
        await page.click('#Username')
        await page.click('.btn.signin-btn.bg-primary.ng-tns-c813547932-0')
        await page.click('span')
        await page.click('.btn-ts.btn-ts-primary')
        await page.click('.ng-value-container')
        await page.click('#a9ae644fd115-0')
        await page.click('#stream-title')
        await page.click('#stream-desc')
        await page.click('.croppie-input')
        await page.click('.btn-ts.btn-ts-primary.ng-star-inserted')
        await page.click('#stream-desc')
        await browser.close()
asyncio.run(test_recorded_actions())