{% extends 'base.html' %}

{% block title %}Running Test: {{ test_case.name }} - StreamzAI Test Generator{% endblock %}

{% block header %}Running Test: {{ test_case.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Execution</h5>
                <div>
                    <a href="{{ url_for('test_case_detail', case_id=test_case.id) }}" class="btn btn-sm btn-secondary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Test Case
                    </a>
                    <button id="stopTestBtn" class="btn btn-sm btn-danger">
                        <i class="bi bi-stop-fill"></i> Stop Test
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ test_case.name }}</p>
                        <p>
                            <strong>Test Suite:</strong>
                            <a href="{{ url_for('test_suite_detail', suite_id=test_case.test_suite_id) }}">
                                {{ test_case.test_suite.name }}
                            </a>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Status:</strong> <span id="testStatus" class="badge bg-warning">Running</span></p>
                        <p><strong>Started:</strong> <span id="startTime">{{ start_time.strftime('%Y-%m-%d %H:%M:%S') }}</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Console Output</h5>
                <div>
                    <button id="clearConsoleBtn" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-trash"></i> Clear Console
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="console" class="console-output">
                    <div class="console-inner" id="consoleInner">
                        <div class="console-line">Starting test execution...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Steps Progress</h5>
            </div>
            <div class="card-body">
                <div class="progress" style="height: 25px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div class="mt-3">
                    <div id="stepStats" class="d-flex justify-content-between">
                        <span><strong>Total:</strong> <span id="totalSteps">{{ action_steps|length }}</span></span>
                        <span><strong>Completed:</strong> <span id="completedSteps">0</span></span>
                        <span><strong>Passed:</strong> <span id="passedSteps">0</span></span>
                        <span><strong>Failed:</strong> <span id="failedSteps">0</span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Current Step</h5>
            </div>
            <div class="card-body">
                <div id="currentStepInfo" class="alert alert-info">
                    Waiting for test to start...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .console-output {
        background-color: #000;
        color: #00ff00;
        font-family: 'Courier New', monospace;
        height: 400px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 0 0 4px 4px;
    }

    .console-inner {
        min-height: 100%;
    }

    .console-line {
        line-height: 1.5;
        margin-bottom: 2px;
        white-space: pre-wrap;
        word-break: break-all;
    }

    .console-error {
        color: #ff5555;
    }

    .console-warning {
        color: #ffff55;
    }

    .console-success {
        color: #55ff55;
    }

    .console-info {
        color: #5555ff;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const consoleOutput = document.getElementById('consoleInner');
        const progressBar = document.getElementById('progressBar');
        const testStatus = document.getElementById('testStatus');
        const totalSteps = document.getElementById('totalSteps');
        const completedSteps = document.getElementById('completedSteps');
        const passedSteps = document.getElementById('passedSteps');
        const failedSteps = document.getElementById('failedSteps');
        const currentStepInfo = document.getElementById('currentStepInfo');
        const stopTestBtn = document.getElementById('stopTestBtn');
        const clearConsoleBtn = document.getElementById('clearConsoleBtn');

        let eventSource;
        let isTestRunning = true;
        let totalStepsCount = parseInt(totalSteps.textContent) || 0;
        let completedStepsCount = 0;
        let passedStepsCount = 0;
        let failedStepsCount = 0;

        // Function to add a line to the console
        function addConsoleMessage(message, type = 'normal') {
            const line = document.createElement('div');
            line.className = 'console-line';

            if (type === 'error') {
                line.classList.add('console-error');
            } else if (type === 'warning') {
                line.classList.add('console-warning');
            } else if (type === 'success') {
                line.classList.add('console-success');
            } else if (type === 'info') {
                line.classList.add('console-info');
            }

            line.textContent = message;
            consoleOutput.appendChild(line);

            // Auto-scroll to bottom
            consoleOutput.parentElement.scrollTop = consoleOutput.parentElement.scrollHeight;
        }

        // Function to update progress
        function updateProgress() {
            const percentage = totalStepsCount > 0 ? Math.round((completedStepsCount / totalStepsCount) * 100) : 0;
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);

            completedSteps.textContent = completedStepsCount;
            passedSteps.textContent = passedStepsCount;
            failedSteps.textContent = failedStepsCount;
        }

        // Connect to SSE endpoint
        function connectEventSource() {
            eventSource = new EventSource('/test-case/{{ test_case.id }}/stream');

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);

                switch (data.type) {
                    case 'console':
                        addConsoleMessage(data.data, data.level || 'normal');
                        break;

                    case 'step':
                        // Update current step info
                        currentStepInfo.innerHTML = `
                            <strong>Action:</strong> ${data.data.action} |
                            <strong>Selector:</strong> ${data.data.selector || 'N/A'} |
                            <strong>Value:</strong> ${data.data.value || 'N/A'}
                        `;

                        // Update step counts
                        completedStepsCount++;
                        if (data.data.status === 'passed') {
                            passedStepsCount++;
                            currentStepInfo.className = 'alert alert-success';
                        } else {
                            failedStepsCount++;
                            currentStepInfo.className = 'alert alert-danger';
                        }

                        updateProgress();
                        break;

                    case 'status':
                        if (data.data === 'completed') {
                            testStatus.textContent = 'Completed';
                            testStatus.className = 'badge bg-success';
                            isTestRunning = false;

                            if (eventSource) {
                                eventSource.close();
                            }

                            addConsoleMessage('Test execution completed.', 'success');
                        } else if (data.data === 'failed') {
                            testStatus.textContent = 'Failed';
                            testStatus.className = 'badge bg-danger';
                            isTestRunning = false;

                            if (eventSource) {
                                eventSource.close();
                            }

                            addConsoleMessage('Test execution failed.', 'error');
                        } else if (data.data === 'not_running') {
                            testStatus.textContent = 'Not Running';
                            testStatus.className = 'badge bg-secondary';
                            isTestRunning = false;

                            if (eventSource) {
                                eventSource.close();
                            }

                            addConsoleMessage('Test is not currently running.', 'info');
                        } else if (data.data === 'timeout') {
                            testStatus.textContent = 'Timeout';
                            testStatus.className = 'badge bg-warning';
                            isTestRunning = false;

                            if (eventSource) {
                                eventSource.close();
                            }

                            addConsoleMessage('Connection timed out.', 'warning');
                        }
                        break;

                    case 'error':
                        addConsoleMessage(`Error: ${data.data}`, 'error');
                        break;
                }
            };

            eventSource.onerror = function() {
                if (isTestRunning) {
                    addConsoleMessage('Connection to server lost.', 'warning');
                    // Don't automatically reconnect to prevent infinite loops
                    // User can refresh the page if needed
                    isTestRunning = false;
                    testStatus.textContent = 'Connection Lost';
                    testStatus.className = 'badge bg-warning';
                }
            };
        }

        // Initialize connection
        connectEventSource();

        // Stop test button
        if (stopTestBtn) {
            stopTestBtn.addEventListener('click', function() {
                if (!isTestRunning) return;

                if (confirm('Are you sure you want to stop the test execution?')) {
                    fetch('/test-case/{{ test_case.id }}/stop', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        addConsoleMessage('Test execution stopped by user.', 'warning');
                        testStatus.textContent = 'Stopped';
                        testStatus.className = 'badge bg-secondary';
                        isTestRunning = false;

                        if (eventSource) {
                            eventSource.close();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        addConsoleMessage(`Error stopping test: ${error.message}`, 'error');
                    });
                }
            });
        }

        // Clear console button
        if (clearConsoleBtn) {
            clearConsoleBtn.addEventListener('click', function() {
                consoleOutput.innerHTML = '';
                addConsoleMessage('Console cleared.', 'info');
            });
        }

        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    });
</script>
{% endblock %}
