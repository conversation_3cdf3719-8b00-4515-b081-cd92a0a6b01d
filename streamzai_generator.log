2025-04-21 14:40:21,414 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-21 14:40:21,414 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-21 14:40:21,416 - werkzeug - INFO -  * Restarting with stat
2025-04-21 14:40:23,358 - werkzeug - WARNING -  * Debugger is active!
2025-04-21 14:40:23,358 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-21 14:40:23,929 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:23] "GET /test-suite/create HTTP/1.1" 200 -
2025-04-21 14:40:23,958 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-21 14:40:23,958 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:23] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-21 14:40:35,445 - StreamzAI - WARNING - No Google API key provided. Please set GOOGLE_API_KEY environment variable or provide it as an argument.
2025-04-21 14:40:35,448 - StreamzAI - ERROR - Project path does not exist: C:\Users\<USER>\Documents\Desktop\Classroom\Projects\Portfolio\portfolio
2025-04-21 14:40:35,448 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:35] "[32mPOST /test-suite/create HTTP/1.1[0m" 302 -
2025-04-21 14:40:35,505 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:35] "GET /test-suites HTTP/1.1" 200 -
2025-04-21 14:40:35,523 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-21 14:40:35,526 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:35] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-21 14:40:50,625 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:50] "[32mPOST /test-suite/15/delete HTTP/1.1[0m" 302 -
2025-04-21 14:40:50,638 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:50] "GET /test-suites HTTP/1.1" 200 -
2025-04-21 14:40:50,654 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-21 14:40:50,657 - werkzeug - INFO - 127.0.0.1 - - [21/Apr/2025 14:40:50] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 10:05:28,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-22 10:05:28,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-22 10:05:28,429 - werkzeug - INFO -  * Restarting with stat
2025-04-22 10:05:30,235 - werkzeug - WARNING -  * Debugger is active!
2025-04-22 10:05:30,235 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-22 10:05:56,517 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 10:05:56] "GET /test-suites HTTP/1.1" 200 -
2025-04-22 10:05:56,611 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 10:05:56] "GET / HTTP/1.1" 200 -
2025-04-22 10:05:56,876 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 10:05:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 10:05:56,893 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 10:05:56] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 10:05:57,011 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 10:05:57] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-04-22 14:20:15,278 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-22 14:20:15,278 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-22 14:20:15,284 - werkzeug - INFO -  * Restarting with stat
2025-04-22 14:20:18,105 - werkzeug - WARNING -  * Debugger is active!
2025-04-22 14:20:18,106 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-22 14:20:18,146 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:18] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-04-22 14:20:18,206 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:18] "GET / HTTP/1.1" 200 -
2025-04-22 14:20:18,233 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:20:18,273 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:20:20,267 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:20] "GET /projects HTTP/1.1" 200 -
2025-04-22 14:20:20,292 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:20:20,304 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:20] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:20:21,953 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:21] "GET /test-suites HTTP/1.1" 200 -
2025-04-22 14:20:21,975 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:20:21,986 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:20:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:21:01,952 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:01] "GET /project/1 HTTP/1.1" 200 -
2025-04-22 14:21:01,974 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:21:01,984 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:01] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:21:13,153 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:13] "GET /test-runs HTTP/1.1" 200 -
2025-04-22 14:21:13,181 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:21:13,189 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:13] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:21:15,313 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:15] "GET /test-run/4 HTTP/1.1" 200 -
2025-04-22 14:21:15,347 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:21:15,354 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:21:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-22 14:27:57,504 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:27:57] "[33mPOST /stop-recording HTTP/1.1[0m" 404 -
2025-04-22 14:32:08,949 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:32:08] "GET / HTTP/1.1" 200 -
2025-04-22 14:32:08,968 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:32:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-22 14:32:08,968 - werkzeug - INFO - 127.0.0.1 - - [22/Apr/2025 14:32:08] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:19:16,002 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\migrations'.  Please use the 'init' command to create a new scripts folder.
2025-04-23 12:21:45,741 - flask_migrate - ERROR - Error: Directory migrations already exists and is not empty
2025-04-23 12:30:51,921 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-23 12:30:51,921 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-23 12:30:51,926 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:30:54,534 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:30:54,534 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 12:30:54,534 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:54] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-04-23 12:30:54,634 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:54] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:30:54,656 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:54] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:30:54,656 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:30:56,670 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:56] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:30:56,683 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:30:56,688 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:56] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:30:58,045 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:58] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:30:58,068 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:30:58,072 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:30:59,657 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:59] "GET /test-suites HTTP/1.1" 200 -
2025-04-23 12:30:59,689 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:30:59,697 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:30:59] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:31:16,384 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:31:16] "[32mPOST /test-suite/16/delete HTTP/1.1[0m" 302 -
2025-04-23 12:31:16,400 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:31:16] "GET /test-suites HTTP/1.1" 200 -
2025-04-23 12:31:16,422 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:31:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:31:16,434 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:31:16] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:32:03,550 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:03] "GET /test-suite/create HTTP/1.1" 200 -
2025-04-23 12:32:03,574 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:32:03,585 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:03] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:32:09,938 - StreamzAI - WARNING - No Google API key provided. Please set GOOGLE_API_KEY environment variable or provide it as an argument.
2025-04-23 12:32:09,955 - StreamzAI - ERROR - Project path does not exist: C:\Users\<USER>\Documents\Desktop\Classroom\Projects\Portfolio\portfolio
2025-04-23 12:32:09,955 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:09] "[32mPOST /test-suite/create HTTP/1.1[0m" 302 -
2025-04-23 12:32:09,974 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:09] "GET /test-suites HTTP/1.1" 200 -
2025-04-23 12:32:10,007 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:32:10,011 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:32:10] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:36:20,184 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-04-23 12:36:20,496 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:36:23,122 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:36:23,122 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 12:36:32,212 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-04-23 12:36:32,483 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:36:35,217 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:36:35,222 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 12:36:55,946 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:55] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:36:55,969 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:36:55,972 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:55] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:36:57,122 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:57] "GET / HTTP/1.1" 200 -
2025-04-23 12:36:57,142 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:36:57,152 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:57] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:36:58,092 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:58] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:36:58,120 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:36:58,125 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:36:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:37:03,155 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:03] "[32mPOST /project/1/delete HTTP/1.1[0m" 302 -
2025-04-23 12:37:03,155 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:03] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:37:03,181 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:37:03,187 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:03] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:37:05,850 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:05] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:37:05,865 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:37:05,869 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:05] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:37:06,687 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:06] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:37:06,703 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:37:06,705 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:37:06] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:38:02,939 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-04-23 12:38:03,249 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:38:05,851 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:38:05,851 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 12:38:15,051 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:15] "[32mPOST /project/1/delete HTTP/1.1[0m" 302 -
2025-04-23 12:38:15,067 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:15] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:38:15,086 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:38:15,087 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:38:16,881 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:16] "GET /projects HTTP/1.1" 200 -
2025-04-23 12:38:16,899 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-23 12:38:16,903 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 12:38:16] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-23 12:38:57,255 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-04-23 12:38:57,559 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:38:59,900 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:38:59,903 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 12:39:07,980 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-04-23 12:39:08,259 - werkzeug - INFO -  * Restarting with stat
2025-04-23 12:39:10,622 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 12:39:10,629 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 20:16:14,302 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-23 20:16:14,302 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-23 20:16:14,302 - werkzeug - INFO -  * Restarting with stat
2025-04-23 20:16:16,960 - werkzeug - WARNING -  * Debugger is active!
2025-04-23 20:16:16,961 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-23 21:47:03,628 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 21:47:03] "GET /test-suites HTTP/1.1" 200 -
2025-04-23 21:47:03,650 - werkzeug - INFO - 127.0.0.1 - - [23/Apr/2025 21:47:03] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-04-25 14:42:13,499 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-25 14:42:13,505 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-04-25 14:42:13,512 - werkzeug - INFO -  * Restarting with stat
2025-04-25 14:42:15,584 - werkzeug - WARNING -  * Debugger is active!
2025-04-25 14:42:15,586 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-04-25 14:42:21,032 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:21] "GET /projects HTTP/1.1" 200 -
2025-04-25 14:42:21,074 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:21] "GET / HTTP/1.1" 200 -
2025-04-25 14:42:21,486 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-25 14:42:21,519 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:21] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-04-25 14:42:21,544 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-25 14:42:23,611 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:23] "GET /projects HTTP/1.1" 200 -
2025-04-25 14:42:23,646 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-25 14:42:23,681 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:23] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-25 14:42:26,031 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:26] "GET /test-suites HTTP/1.1" 200 -
2025-04-25 14:42:26,052 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-25 14:42:26,059 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:26] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-25 14:42:57,571 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:57] "GET /projects HTTP/1.1" 200 -
2025-04-25 14:42:57,584 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-25 14:42:57,591 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:57] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-04-25 14:42:59,120 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:59] "GET /project/1 HTTP/1.1" 200 -
2025-04-25 14:42:59,141 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-04-25 14:42:59,147 - werkzeug - INFO - 127.0.0.1 - - [25/Apr/2025 14:42:59] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:22:58,476 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-06 20:22:58,477 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-06 20:22:58,487 - werkzeug - INFO -  * Restarting with stat
2025-05-06 20:23:01,433 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 20:23:01,452 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 20:23:05,649 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:05] "GET / HTTP/1.1" 200 -
2025-05-06 20:23:05,693 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:05] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-06 20:23:05,693 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:05] "GET /static/js/main.js HTTP/1.1" 200 -
2025-05-06 20:23:06,549 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:06] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-06 20:23:27,072 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:27] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 20:23:27,107 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:23:27,431 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:27] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:23:34,195 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:34] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 20:23:34,236 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:23:34,237 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:23:46,759 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:46] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 20:23:46,800 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:23:46,801 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:23:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:24:02,432 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:02] "GET /test-suite/3/record HTTP/1.1" 200 -
2025-05-06 20:24:02,483 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:24:02,484 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:02] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:24:30,443 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:30] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 20:24:30,483 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:24:30,486 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:30] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:24:54,234 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:54] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 20:24:54,281 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:24:54,283 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:24:54] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:25:14,056 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:25:14] "[35m[1mPOST /api/record/start HTTP/1.1[0m" 500 -
2025-05-06 20:25:33,355 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:25:33] "[35m[1mPOST /api/record/start HTTP/1.1[0m" 500 -
2025-05-06 20:26:27,649 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-06 20:26:27,650 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-06 20:26:27,652 - werkzeug - INFO -  * Restarting with stat
2025-05-06 20:26:30,508 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 20:26:30,529 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 20:27:10,873 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:10] "GET /projects HTTP/1.1" 200 -
2025-05-06 20:27:10,910 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:27:10,911 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:10] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:27:14,377 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:14] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 20:27:14,459 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:27:14,766 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:27:14] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:30:21,006 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:21] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 20:30:21,058 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:30:21,059 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:30:34,655 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:34] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 20:30:34,703 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:30:34,703 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:30:41,532 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:41] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 20:30:41,568 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:30:41,571 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:30:41] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 20:32:12,201 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:32:12] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-06 20:32:12,269 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:32:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 20:32:12,271 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 20:32:12] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 21:03:05,902 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:03:05] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:03:05,937 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:03:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 21:03:05,938 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:03:05] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 21:12:34,627 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:12:34] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:12:34,699 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:12:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 21:12:34,702 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:12:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 21:18:58,878 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 21:18:59,776 - werkzeug - INFO -  * Restarting with stat
2025-05-06 21:19:05,730 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 21:19:05,782 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 21:19:25,318 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:25] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-06 21:19:25,382 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 21:19:25,383 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:25] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 21:19:29,410 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:29] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:19:40,080 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:40] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:19:47,602 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:19:47] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:22:35,749 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 21:22:36,436 - werkzeug - INFO -  * Restarting with stat
2025-05-06 21:22:41,870 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 21:22:41,918 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 21:25:38,583 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:25:38] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 21:25:38,666 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:25:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 21:25:38,668 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 21:25:38] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:07:36,112 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:07:36] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-06 22:38:36,397 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:38:36] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 22:38:36,416 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:38:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 22:38:36,858 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:38:36] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:38:39,074 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:38:39] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 22:38:50,071 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:38:50] "[35m[1mPOST /api/record/stop HTTP/1.1[0m" 500 -
2025-05-06 22:41:09,756 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:09] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 22:41:09,791 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 22:41:09,794 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:41:16,316 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:16] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-06 22:41:16,339 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 22:41:16,340 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:16] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:41:21,245 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:21] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 22:41:21,293 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 22:41:21,293 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:41:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:47:38,810 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:38] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 22:47:41,767 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:41] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 22:47:41,819 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:41] "GET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-06 22:47:41,830 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:41] "GET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-06 22:47:42,283 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:42] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=CHg7nrDoIU3sSTA6PAh2 HTTP/1.1" 200 -
2025-05-06 22:47:42,283 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:42] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-06 22:47:42,311 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:47:42] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 22:53:08,646 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:53:08] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 22:53:08,696 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:53:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 22:53:09,191 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:53:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 22:53:11,320 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 22:53:11] "GET /test-suite/13/record HTTP/1.1" 200 -
2025-05-06 23:00:34,052 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:00:35,355 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:00:44,160 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:00:44,215 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:01:29,075 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:01:29] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:01:29,119 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:01:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:01:30,038 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:01:30] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:01:30,039 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:01:30] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-06 23:02:38,580 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:38] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:02:40,249 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:40] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:02:48,367 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:48] "[35m[1mGET /api/record/start?url=https://mail.google.com/ HTTP/1.1[0m" 500 -
2025-05-06 23:02:52,650 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:52] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:02:57,396 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:57] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:02:57,428 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:02:57,844 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:57] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:02:57,846 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:02:57] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-06 23:03:09,468 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:03:09] "[35m[1mGET /api/record/start?url=https://mail.google.com/ HTTP/1.1[0m" 500 -
2025-05-06 23:03:13,202 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:03:13] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:04:04,326 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:04:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:04:04,372 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:04:04] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-06 23:12:12,866 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:12:13,834 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:12:20,305 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:12:20,359 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:12:26,976 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:26] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:12:26,978 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:26] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:12:30,175 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:30] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:12:30,238 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:12:31,301 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:31] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:12:31,302 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:31] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-06 23:12:50,028 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:50] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:12:50,030 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:12:50] "GET /api/record/start?url=https://in.pinterest.com/pin/71494712829661135/ HTTP/1.1" 200 -
2025-05-06 23:13:11,432 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:11] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:11,474 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:11] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:11,476 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:11] "GET /api/record/start?url=https://in.pinterest.com/pin/71494712829661135/ HTTP/1.1" 200 -
2025-05-06 23:13:29,522 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:29] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:29,922 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:29] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:31,588 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:31] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:31,828 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:31] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:13:31,830 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:13:31] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:15:06,456 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:15:07,221 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:15:13,597 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:15:13,660 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:15:25,986 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:15:26,676 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:15:32,684 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:15:32,736 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:15:45,656 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:15:46,374 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:15:52,074 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:15:52,150 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:16:28,869 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:28] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:16:28,932 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:16:29,369 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:29] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:16:29,370 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:29] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-06 23:16:36,228 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:36] "POST /api/record/action HTTP/1.1" 200 -
2025-05-06 23:16:36,232 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:36] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:16:51,401 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:51] "GET /api/record/start?url=https://mail.google.com/ HTTP/1.1" 200 -
2025-05-06 23:16:51,463 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:51] "[35m[1mPOST /api/record/action HTTP/1.1[0m" 500 -
2025-05-06 23:16:51,478 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:16:51] "[35m[1mPOST /api/record/action HTTP/1.1[0m" 500 -
2025-05-06 23:17:04,628 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:17:05,488 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:17:11,778 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:17:11,838 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:20:20,707 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:20:21,362 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:20:27,370 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:20:27,423 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:22:44,460 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:44] "[33mPOST /api/record/action HTTP/1.1[0m" 404 -
2025-05-06 23:22:46,423 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:46] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:22:46,484 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:22:46,486 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:46] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-06 23:22:46,939 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:22:59,408 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:22:59] "POST /api/record/start HTTP/1.1" 200 -
2025-05-06 23:23:11,762 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:11] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:23:11,823 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:23:11,825 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:11] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-06 23:23:12,212 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:12] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:23:24,250 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:24] "POST /api/record/start HTTP/1.1" 200 -
2025-05-06 23:23:38,158 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:38] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-06 23:23:38,323 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:23:38] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-06 23:24:16,272 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:24:16] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-06 23:29:11,677 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:29:11] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 23:29:12,154 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:29:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:29:12,161 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:29:12] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:32:31,141 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:32:37,775 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:32:57,720 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:32:57,763 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:34:12,524 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-06 23:34:12,524 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-06 23:34:12,532 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:34:15,051 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:34:15,075 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:34:48,784 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:34:48] "POST /api/record/start HTTP/1.1" 200 -
2025-05-06 23:34:57,982 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:34:57] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-06 23:35:14,639 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:35:14] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-06 23:35:32,616 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:35:32] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 23:35:32,654 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:35:32,654 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:35:32] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:40:33,979 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:40:34,400 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:40:34,654 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:40:34,904 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:40:56,507 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:40:56,534 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:41:02,761 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:41:03,183 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\app.py', reloading
2025-05-06 23:41:03,255 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:41:03,702 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:41:06,038 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:41:06,061 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:41:06,438 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:41:06,461 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:43:00,261 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:43:00,304 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:43:00,311 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:43:00,592 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=6MoMBjYITDS47LIIkRs7 HTTP/1.1" 200 -
2025-05-06 23:43:00,602 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:43:00,629 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:43:00] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:44:25,629 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\migrate_db.py', reloading
2025-05-06 23:44:26,384 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:44:29,289 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:44:29,311 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:45:00,367 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\init_db.py', reloading
2025-05-06 23:45:00,857 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:45:00,951 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\init_db.py', reloading
2025-05-06 23:45:01,457 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:45:03,676 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:45:03,702 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:45:04,274 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:45:04,306 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:48:26,250 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\create_db.py', reloading
2025-05-06 23:48:26,592 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\create_db.py', reloading
2025-05-06 23:48:26,819 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:48:27,138 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:48:29,732 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:48:29,753 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:48:29,906 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:48:29,929 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:49:03,652 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:03] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:49:03,684 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:49:03,684 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:49:04,180 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:04] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=J1kyQiYrmcRA6tA97QRH HTTP/1.1" 200 -
2025-05-06 23:49:04,187 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:04,202 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:05,531 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:49:05,555 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:49:05,556 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:49:05,902 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=J1kyQiYrmcRA6tA97QRH HTTP/1.1[0m" 304 -
2025-05-06 23:49:05,904 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:05,918 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:05] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:06,630 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:49:06,650 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:49:06,651 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:49:06,841 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=J1kyQiYrmcRA6tA97QRH HTTP/1.1[0m" 304 -
2025-05-06 23:49:06,847 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:06,872 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:06] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:07,215 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:49:07,244 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:49:07,245 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:49:07,427 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=J1kyQiYrmcRA6tA97QRH HTTP/1.1[0m" 304 -
2025-05-06 23:49:07,438 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:07,459 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:07] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:11,513 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-06 23:49:11,540 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:49:11,541 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:49:11,720 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=J1kyQiYrmcRA6tA97QRH HTTP/1.1[0m" 304 -
2025-05-06 23:49:11,728 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:49:11,748 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:51:01,588 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\create_db.py', reloading
2025-05-06 23:51:01,953 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\create_db.py', reloading
2025-05-06 23:51:02,164 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:51:02,684 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:51:05,049 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:51:05,070 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:51:05,445 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:51:05,474 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:53:37,646 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-06 23:53:37,646 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-06 23:53:37,647 - werkzeug - INFO -  * Restarting with stat
2025-05-06 23:53:40,433 - werkzeug - WARNING -  * Debugger is active!
2025-05-06 23:53:40,455 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-06 23:56:46,669 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:56:46] "GET /test-suites HTTP/1.1" 200 -
2025-05-06 23:56:46,711 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:56:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:56:46,713 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:56:46] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-06 23:56:47,226 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:56:47] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:57:14,444 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:14] "POST /api/record/start HTTP/1.1" 200 -
2025-05-06 23:57:15,420 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:15] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-06 23:57:22,930 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:22] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-06 23:57:27,800 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:27] "POST /api/record/save HTTP/1.1" 200 -
2025-05-06 23:57:29,898 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:29] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-06 23:57:29,946 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-06 23:57:29,951 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:29] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-06 23:57:29,952 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:29] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-06 23:57:43,192 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "[35m[1mGET /test-case/1 HTTP/1.1[0m" 500 -
2025-05-06 23:57:43,235 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "GET /test-case/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-06 23:57:43,236 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "GET /test-case/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-06 23:57:43,613 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "GET /test-case/1?__debugger__=yes&cmd=resource&f=console.png&s=TSWE2xjkzcBQGQwAHiqn HTTP/1.1" 200 -
2025-05-06 23:57:43,615 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "GET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-06 23:57:43,641 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:43] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:57:49,627 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:49] "[35m[1mGET /test-case/1 HTTP/1.1[0m" 500 -
2025-05-06 23:57:49,662 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:49] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:57:49,665 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:49] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:57:50,062 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:50] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png&s=TSWE2xjkzcBQGQwAHiqn HTTP/1.1[0m" 304 -
2025-05-06 23:57:50,063 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:50] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-06 23:57:57,398 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:57:57] "GET /test-case/1/download HTTP/1.1" 200 -
2025-05-06 23:58:02,418 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:58:02] "[35m[1mGET /test-case/1 HTTP/1.1[0m" 500 -
2025-05-06 23:58:02,462 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:58:02] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-06 23:58:02,463 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:58:02] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-06 23:58:02,766 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:58:02] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png&s=TSWE2xjkzcBQGQwAHiqn HTTP/1.1[0m" 304 -
2025-05-06 23:58:02,766 - werkzeug - INFO - 127.0.0.1 - - [06/May/2025 23:58:02] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 00:00:19,121 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:00:19] "[35m[1mGET /test-case/1 HTTP/1.1[0m" 500 -
2025-05-07 00:00:19,151 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:00:19] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 00:00:19,153 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:00:19] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 00:00:19,735 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:00:19] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png&s=TSWE2xjkzcBQGQwAHiqn HTTP/1.1[0m" 304 -
2025-05-07 00:00:19,737 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:00:19] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 00:01:04,780 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:01:04] "[35m[1mGET /test-case/1 HTTP/1.1[0m" 500 -
2025-05-07 00:01:04,840 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:01:04] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 00:01:04,840 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:01:04] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 00:01:05,171 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:01:05] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png&s=TSWE2xjkzcBQGQwAHiqn HTTP/1.1[0m" 304 -
2025-05-07 00:01:05,173 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:01:05] "[36mGET /test-case/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 00:02:33,198 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\models.py', reloading
2025-05-07 00:02:33,938 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:34,366 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\models.py', reloading
2025-05-07 00:02:34,559 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\models.py', reloading
2025-05-07 00:02:35,046 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:35,211 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:37,342 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:37,363 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:02:38,267 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:38,300 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:02:38,604 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:38,633 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:02:49,991 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\add_active_to_test_result.py', reloading
2025-05-07 00:02:50,237 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\add_active_to_test_result.py', reloading
2025-05-07 00:02:50,812 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:51,065 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:51,277 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\test-generation\\add_active_to_test_result.py', reloading
2025-05-07 00:02:51,885 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:02:54,359 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:54,382 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:02:54,551 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:54,577 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:02:55,369 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:02:55,395 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:03:06,478 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 00:03:06,478 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 00:03:06,480 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:03:09,215 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:03:09,242 - werkzeug - INFO -  * Debugger PIN: 427-303-074
2025-05-07 00:06:18,394 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:06:18] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 00:06:18,438 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:06:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:06:18,441 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:06:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:06,841 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 00:32:06,856 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 00:32:06,863 - werkzeug - INFO -  * Restarting with stat
2025-05-07 00:32:23,986 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 00:32:23,992 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 00:32:24,090 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "[33mGET /event-stream HTTP/1.1[0m" 404 -
2025-05-07 00:32:24,097 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "[33mGET /event-stream HTTP/1.1[0m" 404 -
2025-05-07 00:32:24,311 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 00:32:24,475 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:24,713 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:24,716 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:24] "GET /static/js/recorder.js HTTP/1.1" 200 -
2025-05-07 00:32:27,383 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:27] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 00:32:27,402 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:27,414 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:27] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:27,417 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:27] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:30,544 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:30] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 00:32:30,573 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:30,594 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:30] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:35,030 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:35] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 00:32:35,054 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:35,074 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:35] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:35,075 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:35] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:44,601 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:44] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 00:32:44,633 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:44,645 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:44] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:32:58,405 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:58] "GET /projects HTTP/1.1" 200 -
2025-05-07 00:32:58,437 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:32:58,449 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:32:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:00,903 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:00] "GET /projects HTTP/1.1" 200 -
2025-05-07 00:33:00,916 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:33:00,932 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:00] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:01,803 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:01] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 00:33:01,835 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:33:01,849 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:01] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:01,854 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:01] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:03,091 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:03] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 00:33:03,118 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:33:03,133 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:03] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:03,134 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:03] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 00:33:12,013 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:12] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 00:33:12,052 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 00:33:12,067 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 00:33:12] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 01:14:44,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 01:14:44,429 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 01:14:44,429 - werkzeug - INFO -  * Restarting with stat
2025-05-07 01:14:47,799 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 01:14:47,799 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 01:15:09,253 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:09] "GET / HTTP/1.1" 200 -
2025-05-07 01:15:09,285 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 01:15:09,287 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 01:15:44,889 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:44] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 01:15:44,933 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 01:15:44,939 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:44] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 01:15:44,942 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:44] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 01:15:47,539 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 01:15:47] "GET /test-case/1/download HTTP/1.1" 200 -
2025-05-07 08:34:12,582 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:34:12,582 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:34:12,590 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:34:15,667 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:34:15,678 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:34:15,846 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:15] "GET / HTTP/1.1" 200 -
2025-05-07 08:34:15,960 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:34:15,962 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:34:28,165 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:34:28,204 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:28] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:34:41,605 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:41] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 08:34:41,634 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:34:41,650 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:34:41] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:42:55,031 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:42:55,031 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:42:55,035 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:42:58,091 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:42:58,091 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:42:59,696 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:42:59] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:42:59,744 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:42:59] "GET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-07 08:42:59,746 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:42:59] "GET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-07 08:42:59,849 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:42:59] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1" 200 -
2025-05-07 08:42:59,879 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:42:59] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-07 08:43:01,060 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:01,076 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,087 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,120 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,136 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,801 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:01,819 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,832 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,849 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:43:01,865 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:01] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:43:02,362 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:02] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:02,379 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:02] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:02,387 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:02] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:02,415 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:02] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:43:02,429 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:02] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:43:03,968 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:03] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:03,983 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:03,983 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,007 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,032 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,410 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:04,425 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,430 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,450 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:43:04,468 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:04] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:43:18,317 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:18] "GET /projects HTTP/1.1" 200 -
2025-05-07 08:43:18,330 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:18,339 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:19,643 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:19] "GET /projects HTTP/1.1" 200 -
2025-05-07 08:43:19,657 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:19,662 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:19] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:20,924 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:20] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:43:20,951 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:20] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:43:20,963 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:20] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:43:20,975 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:43:20] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=F7ZOXLPkZ2auREBluJ0G HTTP/1.1[0m" 304 -
2025-05-07 08:47:22,276 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\api\\test_suite_routes.py', reloading
2025-05-07 08:47:22,941 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:47:26,762 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:47:26,762 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:47:31,871 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\api\\test_suite_routes.py', reloading
2025-05-07 08:47:32,378 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:47:36,013 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:47:36,019 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:47:50,411 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:47:50,411 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:47:50,425 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:47:52,550 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:52] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:52,579 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:52] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:52,590 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:52] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:52,631 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:52] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1" 200 -
2025-05-07 08:47:52,659 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:52] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:53,652 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:53] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:53,676 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:53] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:53,685 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:53] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:53,714 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:53] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:47:53,738 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:53] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,098 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:54,118 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,132 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,158 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,184 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,367 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:54,400 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,409 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,439 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,461 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,703 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:54,735 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,739 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,768 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:47:54,788 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:54] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:55,056 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:55] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:47:55,128 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:55] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:47:55,132 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:55] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:47:55,214 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:55] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:47:55,234 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:47:55] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:47:55,742 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:47:55,744 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:48:03,585 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:03] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:48:03,623 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:48:03,639 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:48:03,699 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:48:03,717 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:03] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:48:09,110 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "GET /projects HTTP/1.1" 200 -
2025-05-07 08:48:09,124 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:48:09,129 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:48:09,773 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "GET /projects HTTP/1.1" 200 -
2025-05-07 08:48:09,797 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:48:09,813 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:48:10,846 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:10] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:48:10,863 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:10] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:48:10,879 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:10] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:48:10,888 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:48:10] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=t11cSrL1vu7NVPcWKx5Z HTTP/1.1[0m" 304 -
2025-05-07 08:49:07,366 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:49:07,366 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:49:07,375 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:49:10,927 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:49:10,935 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:49:11,068 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:49:11] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:49:11,088 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:49:11,090 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:49:11,116 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:49:11] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=X8fna0mioxHlA0QC4P7N HTTP/1.1" 200 -
2025-05-07 08:49:11,144 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:49:11] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:03,820 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\api\\test_suite_routes.py', reloading
2025-05-07 08:50:04,352 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:50:08,999 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:50:09,003 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:50:13,245 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:13] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:13,263 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:13] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:13,275 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:13] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:13,298 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:13] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=5qqIiBixPARmf8CSoarM HTTP/1.1" 200 -
2025-05-07 08:50:13,328 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:13] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:14,190 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:14] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:14,206 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:14] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:14,211 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:14] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:14,219 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:14] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=5qqIiBixPARmf8CSoarM HTTP/1.1[0m" 304 -
2025-05-07 08:50:14,249 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:14] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:25,127 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:50:25,127 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:50:25,134 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:50:28,727 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:50:28,735 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:50:28,909 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:28,909 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:28,917 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:28,927 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:28,937 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:28,960 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "GET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1" 200 -
2025-05-07 08:50:28,977 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:28] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:29,481 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:29] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:29,502 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:29] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:29,505 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:29] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:29,520 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:29] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:29,547 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:29] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,248 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:30,264 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,269 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,285 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,312 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,652 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:30,668 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,674 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,685 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,710 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,962 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:30,981 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:30,987 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:30] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,011 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,028 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,236 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:31,257 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,258 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,279 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,298 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,518 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:31,536 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,541 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,579 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,586 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,652 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:31,667 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,669 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,681 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,703 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,977 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:31,994 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:31,996 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:31] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,012 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,034 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,292 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[35m[1mGET /test-suites HTTP/1.1[0m" 500 -
2025-05-07 08:50:32,301 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,309 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,317 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png&s=DIhRBSvXainmG3ZB5l6W HTTP/1.1[0m" 304 -
2025-05-07 08:50:32,341 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:50:32] "[36mGET /test-suites?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-07 08:52:45,775 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 08:52:45,775 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 08:52:45,780 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:52:50,630 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:52:50,635 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:52:50,817 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:50] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:52:51,042 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:52:51,074 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:51] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 08:52:51,102 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:51] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:52:55,172 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:55] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 08:52:55,204 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:52:55,219 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:55] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:52:58,127 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:58] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:52:58,148 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:52:58,170 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:58] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 08:52:58,170 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:52:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:53:18,416 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:53:18] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:53:18,439 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:53:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:53:18,464 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:53:18] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 08:53:18,468 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:53:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:54:10,957 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:54:10] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:54:10,981 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:54:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:54:10,992 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:54:10] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 08:54:10,996 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:54:10] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:54:28,468 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\api\\test_suite_routes.py', reloading
2025-05-07 08:54:28,878 - werkzeug - INFO -  * Restarting with stat
2025-05-07 08:54:32,240 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 08:54:32,246 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 08:58:15,688 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:15] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 08:58:15,714 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:58:15,720 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:15] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 08:58:15,720 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:58:18,177 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:18] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 08:58:18,205 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:58:18,217 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:58:19,997 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:19] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 08:58:20,012 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:58:20,022 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:20] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 08:58:20,754 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:20] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-07 08:58:20,770 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 08:58:20,772 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 08:58:20] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 09:47:47,844 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 09:47:47,845 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 09:47:47,854 - werkzeug - INFO -  * Restarting with stat
2025-05-07 09:47:50,551 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 09:47:50,573 - werkzeug - INFO -  * Debugger PIN: 117-887-961
2025-05-07 09:47:54,244 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:54] "GET / HTTP/1.1" 200 -
2025-05-07 09:47:54,312 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 09:47:54,357 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:54] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 09:47:55,119 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:55] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-07 09:47:57,801 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:57] "GET /projects HTTP/1.1" 200 -
2025-05-07 09:47:57,841 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 09:47:57,841 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:47:57] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 09:48:00,845 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:00] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 09:48:00,862 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:00] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 09:48:00,900 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 09:48:00,901 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:00] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 09:48:00,902 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:00] "GET /static/css/test-runner.css HTTP/1.1" 200 -
2025-05-07 09:48:01,169 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:01] "GET /static/js/test-runner.js HTTP/1.1" 200 -
2025-05-07 09:48:01,172 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:01] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 09:48:59,582 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:59] "POST /api/test-runner/upload HTTP/1.1" 200 -
2025-05-07 09:48:59,606 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:48:59] "POST /api/test-runner/upload-input HTTP/1.1" 200 -
2025-05-07 09:49:15,160 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 09:49:15] "POST /api/test-runner/run HTTP/1.1" 200 -
2025-05-07 11:08:50,622 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-07 11:08:50,622 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-07 11:08:50,622 - werkzeug - INFO -  * Restarting with stat
2025-05-07 11:08:53,566 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 11:08:53,566 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 11:08:57,853 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 11:08:57,895 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "GET /static/css/test-runner.css HTTP/1.1" 200 -
2025-05-07 11:08:57,911 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:08:57,931 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:08:57,950 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:08:57,992 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:08:57] "GET /static/js/test-runner.js HTTP/1.1" 200 -
2025-05-07 11:09:51,212 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\features\\tests\\test_case.py', reloading
2025-05-07 11:09:51,546 - werkzeug - INFO -  * Restarting with stat
2025-05-07 11:09:54,332 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 11:09:54,350 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 11:10:45,905 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\features\\tests\\test_case.py', reloading
2025-05-07 11:10:46,216 - werkzeug - INFO -  * Restarting with stat
2025-05-07 11:10:48,523 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 11:10:48,523 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 11:13:24,182 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:13:24] "POST /api/test-runner/upload HTTP/1.1" 200 -
2025-05-07 11:13:24,192 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:13:24] "POST /api/test-runner/upload-input HTTP/1.1" 200 -
2025-05-07 11:13:24,252 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\uploads\\test_runner\\test_case.py', reloading
2025-05-07 11:13:24,658 - werkzeug - INFO -  * Restarting with stat
2025-05-07 11:13:27,091 - werkzeug - WARNING -  * Debugger is active!
2025-05-07 11:13:27,091 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-07 11:18:46,702 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:46] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 11:18:46,724 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:18:46,728 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:46] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:18:46,729 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:18:53,998 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:53] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 11:18:54,009 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:18:54,012 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:18:54] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:20:01,079 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:01] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 11:20:01,095 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:20:01,105 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:01] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:20:01,107 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:01] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:20:05,746 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:05] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 11:20:05,761 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:20:05,765 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:05] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:20:16,081 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:16] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 11:20:16,093 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:20:16,097 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:20:16] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:21:18,381 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:18] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 11:21:18,393 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:21:18,399 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:21:18,401 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:18] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:21:28,191 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:28] "GET / HTTP/1.1" 200 -
2025-05-07 11:21:28,203 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:21:28,208 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:21:28] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:24:32,168 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:32] "GET /test-run/6 HTTP/1.1" 200 -
2025-05-07 11:24:32,180 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:24:32,184 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:32] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:24:39,106 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:39] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 11:24:39,117 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:24:39,125 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:39] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:24:39,125 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:39] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:24:42,633 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:42] "GET / HTTP/1.1" 200 -
2025-05-07 11:24:42,645 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:24:42,648 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:42] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:24:48,667 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:48] "GET / HTTP/1.1" 200 -
2025-05-07 11:24:48,677 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:24:48,681 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:24:48] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:25:45,989 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:45] "GET /projects HTTP/1.1" 200 -
2025-05-07 11:25:46,000 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:25:46,004 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:25:48,735 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:48] "GET / HTTP/1.1" 200 -
2025-05-07 11:25:48,747 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:25:48,749 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:48] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:25:55,000 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "GET /test-suites HTTP/1.1" 200 -
2025-05-07 11:25:55,013 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:25:55,017 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-07 11:25:55,022 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:25:55,024 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:25:55,034 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:25:55] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-07 11:46:15,563 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:15] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 11:46:15,584 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:46:15,588 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 11:46:15,588 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:15] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 11:46:21,191 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:21] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 11:46:21,205 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 11:46:21,214 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 11:46:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 13:24:47,860 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:47] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-07 13:24:47,914 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 13:24:47,915 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:47] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-07 13:24:47,918 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:47] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-07 13:24:51,690 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:51] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-07 13:24:51,718 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-07 13:24:51,740 - werkzeug - INFO - 127.0.0.1 - - [07/May/2025 13:24:51] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:28:44,333 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-08 13:28:44,334 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:28:44,335 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:28:46,348 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:28:46,348 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:28:46,416 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:46] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-05-08 13:28:46,416 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:46] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-05-08 13:28:46,416 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:46] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-05-08 13:28:46,416 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:46] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-05-08 13:28:48,749 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:48] "GET / HTTP/1.1" 200 -
2025-05-08 13:28:48,833 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:28:48,835 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:48] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:28:51,187 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:51] "GET /projects HTTP/1.1" 200 -
2025-05-08 13:28:51,200 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:28:51,209 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:51] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:28:52,665 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:52] "GET /project/1 HTTP/1.1" 200 -
2025-05-08 13:28:52,689 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:28:52,694 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:28:52] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:29:32,132 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:32] "GET /projects HTTP/1.1" 200 -
2025-05-08 13:29:32,147 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:29:32,152 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:32] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:29:33,200 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:33] "GET /project/1 HTTP/1.1" 200 -
2025-05-08 13:29:33,214 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:29:33,219 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:33] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:29:38,679 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:38] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:29:38,692 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:29:38,699 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:38] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:29:38,700 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:38] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:29:46,950 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:46] "POST /api/record/start HTTP/1.1" 200 -
2025-05-08 13:29:50,484 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:29:50] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-08 13:30:02,931 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:30:02] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-08 13:34:40,394 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:40] "POST /api/record/save HTTP/1.1" 200 -
2025-05-08 13:34:41,961 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:41] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:34:41,978 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:34:41,985 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:41] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:34:41,989 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:41] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:34:45,646 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:45] "GET /test-case/2 HTTP/1.1" 200 -
2025-05-08 13:34:45,659 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:34:45,665 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:34:45] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:35:21,397 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:21] "GET /test-case/2 HTTP/1.1" 200 -
2025-05-08 13:35:21,409 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:35:21,413 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:35:32,779 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:32] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:35:32,793 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:35:32,800 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:32] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:35:32,801 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:32] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:35:44,380 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:44] "GET /test-case/2 HTTP/1.1" 200 -
2025-05-08 13:35:44,388 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:35:44,397 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:35:44] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:40:30,933 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-05-08 13:40:31,250 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:40:33,417 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:40:33,417 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:40:53,653 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-05-08 13:40:53,933 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:40:56,000 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:40:56,000 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:42:25,100 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:25] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:42:25,132 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:25,133 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:25] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:25,134 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:25] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:28,537 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:28] "GET / HTTP/1.1" 200 -
2025-05-08 13:42:28,557 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:28,558 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:28] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:31,353 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:31] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-08 13:42:31,370 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:31,372 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:31] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:33,713 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:33] "GET /test-suite/13/test-run/create HTTP/1.1" 200 -
2025-05-08 13:42:33,724 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:33,727 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:33] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:44,907 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:44] "GET /projects HTTP/1.1" 200 -
2025-05-08 13:42:44,920 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:44,927 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:44] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:47,620 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:47] "GET /projects HTTP/1.1" 200 -
2025-05-08 13:42:47,630 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:47,633 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:47] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:48,741 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:48] "GET /projects HTTP/1.1" 200 -
2025-05-08 13:42:48,751 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:48,756 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:48] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:49,766 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "GET /test-suites HTTP/1.1" 200 -
2025-05-08 13:42:49,793 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:49,803 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-08 13:42:49,804 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:49,805 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:42:49,813 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:42:49] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-08 13:48:44,903 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-05-08 13:48:45,218 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:48:47,312 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:48:47,313 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:48:58,026 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:48:58] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:48:58,048 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:48:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-08 13:48:58,050 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:48:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:48:58,050 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:48:58] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:49:16,490 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:49:16] "POST /api/record/start HTTP/1.1" 200 -
2025-05-08 13:49:18,727 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:49:18] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-08 13:49:29,589 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:49:29] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-08 13:49:51,990 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-05-08 13:49:52,268 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:49:54,352 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:49:54,366 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:50:07,516 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\StreamzAI\\downloads\\testing\\test-generation1\\test-generation\\app.py', reloading
2025-05-08 13:50:07,785 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:50:09,844 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:50:09,846 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-08 13:50:12,430 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:12] "POST /api/record/start HTTP/1.1" 200 -
2025-05-08 13:50:14,990 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:14] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-08 13:50:26,574 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:26] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-08 13:50:32,217 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:32] "POST /api/record/save HTTP/1.1" 200 -
2025-05-08 13:50:34,135 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:34] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-08 13:50:34,153 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-08 13:50:34,170 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:34] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-08 13:50:34,174 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:50:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:12,463 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-09 15:25:12,463 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-09 15:25:12,465 - werkzeug - INFO -  * Restarting with stat
2025-05-09 15:25:14,278 - werkzeug - WARNING -  * Debugger is active!
2025-05-09 15:25:14,287 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-09 15:25:14,304 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:14] "[33mGET /stream HTTP/1.1[0m" 404 -
2025-05-09 15:25:19,878 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:19] "GET /projects HTTP/1.1" 200 -
2025-05-09 15:25:19,897 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:25:19,949 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:19] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:21,820 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:21] "GET /project/1 HTTP/1.1" 200 -
2025-05-09 15:25:21,837 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:25:21,845 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:26,061 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:26] "GET /test-suite/3 HTTP/1.1" 200 -
2025-05-09 15:25:26,082 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:25:26,094 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:26] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:26,133 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:26] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:35,078 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:35] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-09 15:25:35,097 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:25:35,111 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:35] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:35,111 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:35] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:25:42,326 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:42] "GET /test-case/2 HTTP/1.1" 200 -
2025-05-09 15:25:42,341 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:25:42,347 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:25:42] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:46,199 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:46] "[32mGET /test-suite/3/run HTTP/1.1[0m" 302 -
2025-05-09 15:27:46,256 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:46] "GET /test-suite/3/test-run/create HTTP/1.1" 200 -
2025-05-09 15:27:46,281 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:46,287 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:49,379 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:49] "GET /projects HTTP/1.1" 200 -
2025-05-09 15:27:49,395 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:49,395 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:49] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:50,808 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:50] "GET /projects HTTP/1.1" 200 -
2025-05-09 15:27:50,818 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:50,821 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:50] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:51,619 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 15:27:51,638 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:51,659 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:51,662 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:51,665 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:51,702 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:51] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 15:27:53,630 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:53] "GET /test-suite/13/edit HTTP/1.1" 200 -
2025-05-09 15:27:53,649 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:27:53,663 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:27:53] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:28:28,552 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:28:28] "POST /api/test-runner/upload HTTP/1.1" 200 -
2025-05-09 15:28:28,567 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:28:28] "POST /api/test-runner/upload-input HTTP/1.1" 200 -
2025-05-09 15:29:00,896 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:00] "POST /api/test-runner/run HTTP/1.1" 200 -
2025-05-09 15:29:34,577 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:34] "GET /project/1 HTTP/1.1" 200 -
2025-05-09 15:29:34,592 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:29:34,603 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:29:37,754 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:37] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-09 15:29:37,769 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:29:37,787 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:37] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:29:37,790 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:29:37] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:30:10,637 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:30:10] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-09 15:30:10,652 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:30:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:30:10,660 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:30:10] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:30:10,664 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:30:10] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:32:37,209 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:32:37] "GET /test-case/2 HTTP/1.1" 200 -
2025-05-09 15:32:37,240 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:32:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:32:37,252 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:32:37] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:33:07,039 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 15:33:07,070 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:33:07,086 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 15:33:07,087 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 15:33:07,090 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 15:33:07,099 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:33:07] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 15:43:18,105 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:43:18] "GET / HTTP/1.1" 200 -
2025-05-09 15:43:18,127 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:43:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 15:43:18,130 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 15:43:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 16:21:43,450 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 16:21:43] "GET / HTTP/1.1" 200 -
2025-05-09 21:40:40,747 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-09 21:40:40,749 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-09 21:40:40,750 - werkzeug - INFO -  * Restarting with stat
2025-05-09 21:40:42,460 - werkzeug - WARNING -  * Debugger is active!
2025-05-09 21:40:42,463 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-09 21:40:46,633 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:46] "GET / HTTP/1.1" 200 -
2025-05-09 21:40:46,928 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:40:46,989 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:46] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:40:47,104 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:47] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-09 21:40:56,586 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 21:40:56,612 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:40:56,623 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:40:56,630 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 21:40:56,652 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:40:56,689 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:56] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 21:40:58,218 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:58] "GET /test-runs HTTP/1.1" 200 -
2025-05-09 21:40:58,243 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:40:58,250 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:58] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:40:59,000 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:59] "GET /reports HTTP/1.1" 200 -
2025-05-09 21:40:59,016 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:40:59,023 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:40:59] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:02,411 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:02] "GET /test-runs HTTP/1.1" 200 -
2025-05-09 21:41:02,423 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:02,429 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:02] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:04,284 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "GET / HTTP/1.1" 200 -
2025-05-09 21:41:04,299 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:04,305 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:04,862 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "GET /projects HTTP/1.1" 200 -
2025-05-09 21:41:04,876 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:04,881 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:04] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:06,583 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 21:41:06,602 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:06,620 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:06,628 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:06,630 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:06,637 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:06] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,118 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "GET /projects HTTP/1.1" 200 -
2025-05-09 21:41:07,131 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,139 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,957 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 21:41:07,974 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,984 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,987 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,989 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:07,996 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:07] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 21:41:22,909 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:22] "POST /api/record/start HTTP/1.1" 200 -
2025-05-09 21:41:26,862 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:26] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-09 21:41:42,598 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:42] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-09 21:41:57,905 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:41:57] "POST /api/record/save HTTP/1.1" 200 -
2025-05-09 21:42:02,174 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:02] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-09 21:42:02,199 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:02,209 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:02] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:02,212 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:02] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:09,131 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:09] "GET /test-case/4 HTTP/1.1" 200 -
2025-05-09 21:42:09,148 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:09,160 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:15,384 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:15] "GET /test-runs HTTP/1.1" 200 -
2025-05-09 21:42:15,398 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:15,406 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:15] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:17,721 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:17] "GET /projects HTTP/1.1" 200 -
2025-05-09 21:42:17,738 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:17,743 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:17] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:18,162 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 21:42:18,178 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:18,187 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 21:42:18,191 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:18,194 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:18,200 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:18] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 21:42:30,022 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:30] "POST /api/test-runner/upload HTTP/1.1" 200 -
2025-05-09 21:42:33,603 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:42:33] "POST /api/test-runner/run HTTP/1.1" 200 -
2025-05-09 21:43:09,139 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:09] "GET /test-suite/13/edit HTTP/1.1" 200 -
2025-05-09 21:43:09,152 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:43:09,157 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:09] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:43:16,425 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:16] "GET /projects HTTP/1.1" 200 -
2025-05-09 21:43:16,438 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:43:16,443 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:16] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:43:17,607 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:17] "GET /project/1 HTTP/1.1" 200 -
2025-05-09 21:43:17,619 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:43:17,622 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:17] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:43:21,183 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:21] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-09 21:43:21,195 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:43:21,208 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:21] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:43:21,209 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:43:21] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:44:37,115 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:44:37] "POST /api/record/start HTTP/1.1" 200 -
2025-05-09 21:44:40,146 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:44:40] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-09 21:51:28,250 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:51:28] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-09 21:52:28,548 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-09 21:52:30,624 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 21:52:30,641 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 21:52:30,645 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 21:52:30,652 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 21:52:30,653 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 21:52:30,659 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 21:52:30] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 22:14:31,946 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-09 22:14:31,952 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-09 22:14:31,959 - werkzeug - INFO -  * Restarting with stat
2025-05-09 22:14:34,702 - werkzeug - WARNING -  * Debugger is active!
2025-05-09 22:14:34,705 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-09 22:14:34,764 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:34] "GET /projects HTTP/1.1" 200 -
2025-05-09 22:14:34,796 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 22:14:34,797 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:34] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 22:14:35,847 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 22:14:35,863 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 22:14:35,873 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 22:14:35,877 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 22:14:35,878 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 22:14:35,886 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:35] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
2025-05-09 22:14:51,974 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:51] "POST /api/record/start HTTP/1.1" 200 -
2025-05-09 22:14:52,975 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:14:52] "GET /api/record/stream HTTP/1.1" 200 -
2025-05-09 22:15:35,981 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:35] "POST /api/record/stop HTTP/1.1" 200 -
2025-05-09 22:15:41,243 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:41] "GET /test-suite/13 HTTP/1.1" 200 -
2025-05-09 22:15:41,258 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 22:15:41,265 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:41] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 22:15:41,265 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:41] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 22:15:59,992 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:15:59] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-09 22:16:00,010 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 22:16:00,017 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:00] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 22:16:00,070 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:00] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-09 22:16:18,685 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:18] "GET /test-case/1 HTTP/1.1" 200 -
2025-05-09 22:16:18,701 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 22:16:18,709 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 22:16:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 23:00:44,953 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-09 23:00:44,958 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-09 23:00:44,960 - werkzeug - INFO -  * Restarting with stat
2025-05-09 23:00:48,397 - werkzeug - WARNING -  * Debugger is active!
2025-05-09 23:00:48,403 - werkzeug - INFO -  * Debugger PIN: 510-286-410
2025-05-09 23:00:48,586 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:00:48] "GET /projects HTTP/1.1" 200 -
2025-05-09 23:00:48,776 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:00:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 23:00:48,833 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:00:48] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 23:01:28,094 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:28] "GET /project/1 HTTP/1.1" 200 -
2025-05-09 23:01:28,122 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 23:01:28,138 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:28] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 23:01:31,580 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:31] "GET /test-suite/4 HTTP/1.1" 200 -
2025-05-09 23:01:31,604 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 23:01:31,616 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:31] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 23:01:31,664 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:01:31] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 23:03:02,900 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "GET /test-suites HTTP/1.1" 200 -
2025-05-09 23:03:02,934 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-09 23:03:02,944 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "[36mGET /static/js/recorder.js HTTP/1.1[0m" 304 -
2025-05-09 23:03:02,944 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
2025-05-09 23:03:02,958 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "[36mGET /static/css/test-runner.css HTTP/1.1[0m" 304 -
2025-05-09 23:03:02,990 - werkzeug - INFO - 127.0.0.1 - - [09/May/2025 23:03:02] "[36mGET /static/js/test-runner.js HTTP/1.1[0m" 304 -
