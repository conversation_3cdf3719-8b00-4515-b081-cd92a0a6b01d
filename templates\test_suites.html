{% extends 'base.html' %}

{% block title %}Test Suites - StreamzAI Test Generator{% endblock %}

{% block header %}Test Suites{% endblock %}

{% block head %}
<!-- Add recorder.js script -->
<script src="{{ url_for('static', filename='js/recorder.js') }}"></script>
<!-- Add test runner scripts and styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/test-runner.css') }}">
<script src="{{ url_for('static', filename='js/test-runner.js') }}" defer></script>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2 class="h3 mb-0">Test Suites</h2>
                <p class="text-muted small mb-0">Manage and organize your test collections</p>
            </div>
            <a href="{{ url_for('create_test_suite') }}" class="btn btn-primary d-flex align-items-center gap-2">
                <i class="bi bi-plus-lg"></i>
                New Test Suite
            </a>
        </div>
    </div>

    <!-- Content Section -->
    <div class="row">
        {% if test_suites %}
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="px-4 py-3">Name</th>
                                        <th class="py-3">Description</th>
                                        <th class="py-3 text-center">Test Cases</th>
                                        <th class="py-3 text-center">Test Runs</th>
                                        <th class="py-3">Created</th>
                                        <th class="py-3 text-end pe-4">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for test_suite in test_suites %}
                                    <tr>
                                        <td class="px-4">
                                            <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}" class="text-decoration-none">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-folder me-2 text-primary"></i>
                                                    <span>{{ test_suite.name }}</span>
                                                </div>
                                            </a>
                                        </td>

                                        <td>
                                            <span class="text-muted">{{ test_suite.description|truncate(50) or 'No description' }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ test_suite.test_cases|length }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success">{{ test_suite.test_runs|length }}</span>
                                        </td>
                                        <td>
                                            <span class="text-muted small">
                                                <i class="bi bi-calendar me-1"></i>
                                                {{ test_suite.created_at.strftime('%Y-%m-%d') }}
                                            </span>
                                        </td>
                                        <td class="text-end pe-4">
                                            <div class="btn-group">
                                                <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}"
                                                   class="btn btn-sm btn-outline-secondary"
                                                   data-bs-toggle="tooltip"
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ url_for('edit_test_suite', suite_id=test_suite.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <!-- Run Tests Button (opens test runner modal) -->
                                                <button type="button"
                                                   class="btn btn-sm btn-outline-success"
                                                   data-bs-toggle="tooltip"
                                                   title="Run Tests"
                                                   onclick="openTestRunnerModal('{{ test_suite.id }}')">
                                                    <i class="bi bi-play-fill"></i>
                                                </button>
                                                <!-- Record Test Case Button -->
                                                <button type="button"
                                                        class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#recordModal{{ test_suite.id }}"
                                                        title="Record Test Case">
                                                    <i class="bi bi-record-circle"></i>
                                                </button>
                                                <button type="button"
                                                        class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteTestSuiteModal{{ test_suite.id }}"
                                                        title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>

                                            <!-- Record Modal -->
                                            {% set modal_id = "recordModal" + test_suite.id|string %}
                                            {% set element_suffix = test_suite.id|string %}
                                            {% include 'partials/record_modal.html' %}

                                            <!-- First Delete Modal -->
                                            <div class="modal fade" id="deleteTestSuiteModal{{ test_suite.id }}" tabindex="-1">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content">
                                                        <div class="modal-header border-bottom-0">
                                                            <h5 class="modal-title">Delete Test Suite</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body text-center py-4">
                                                            <i class="bi bi-exclamation-triangle text-warning display-4 mb-3"></i>
                                                            <p class="mb-0">Are you sure you want to delete <strong>{{ test_suite.name }}</strong>?</p>
                                                            <p class="text-muted small">This action cannot be undone and will delete all associated test cases and runs.</p>
                                                        </div>
                                                        <div class="modal-footer border-top-0">
                                                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="button" class="btn btn-danger"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#confirmDeleteModal{{ test_suite.id }}"
                                                                    data-bs-dismiss="modal">
                                                                Delete Test Suite
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Second Confirmation Modal -->
                                            <div class="modal fade" id="confirmDeleteModal{{ test_suite.id }}" tabindex="-1">
                                                <div class="modal-dialog modal-dialog-centered modal-sm">
                                                    <div class="modal-content">
                                                        <div class="modal-body text-center p-4">
                                                            <p class="mb-3">Please confirm deletion of:</p>
                                                            <h6 class="mb-4 text-danger">{{ test_suite.name }}</h6>
                                                            <div class="d-flex justify-content-center gap-2">
                                                                <button type="button" class="btn btn-sm btn-light" data-bs-dismiss="modal">Cancel</button>
                                                                <form action="{{ url_for('delete_test_suite', suite_id=test_suite.id) }}" method="POST" class="d-inline">
                                                                    <button type="submit" class="btn btn-sm btn-danger">Confirm</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-folder-plus display-4 text-muted mb-3"></i>
                        <h5>No Test Suites Found</h5>
                        <p class="text-muted mb-3">Get started by creating your first test suite</p>
                        <a href="{{ url_for('create_test_suite') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg me-1"></i>
                            Create Test Suite
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Test Runner Modal -->
<div class="modal fade" id="testRunnerModal" tabindex="-1" aria-labelledby="testRunnerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="testRunnerModalLabel">Test Runner</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="testRunnerContent">
                    <div id="testConfigSection">
                        <form id="testConfigForm">
                            <input type="hidden" id="suiteId" name="suiteId" value="">

                            <div class="mb-3">
                                <label for="testFile" class="form-label">Choose Test File</label>
                                <input type="file" class="form-control" id="testFile" name="file" accept=".py">
                            </div>

                            <div class="mb-3">
                                <label for="inputMode" class="form-label">Input Mode</label>
                                <select class="form-select" id="inputMode">
                                    <option value="default">Run with default input</option>
                                    <option value="dynamic">Dynamic custom input</option>
                                    <option value="existing">Existing custom input (upload JSON file)</option>
                                </select>
                            </div>

                            <div id="existingInputSection" class="mb-3 d-none">
                                <label for="inputValuesFile" class="form-label">Select Input Values File</label>
                                <input type="file" class="form-control mb-2" id="inputValuesFile" accept=".json">

                                <div class="mt-2">
                                    <label for="inputSet" class="form-label">Select Input Set</label>
                                    <select class="form-select" id="inputSet">
                                        <!-- Will be populated dynamically -->
                                        <option value="" disabled selected>Select a file first</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="headlessMode">
                                <label class="form-check-label" for="headlessMode">Headless Mode</label>
                            </div>

                            <div class="d-grid">
                                <button type="button" id="runTestBtn" class="btn btn-primary btn-lg">Run Test</button>
                            </div>
                        </form>
                    </div>

                    <div id="resultSection" class="d-none mt-4">
                        <h4>Test Results</h4>
                        <div class="alert" id="statusAlert" role="alert"></div>

                        <div class="mb-3">
                            <h5>Output:</h5>
                            <pre id="outputText" class="p-3 bg-light border rounded" style="height: 200px; overflow: auto;"></pre>
                        </div>

                        <div class="mb-3">
                            <h5>Errors:</h5>
                            <pre id="errorText" class="p-3 bg-light border rounded" style="height: 200px; overflow: auto;"></pre>
                        </div>

                        <div class="d-grid mt-3">
                            <button type="button" id="resetBtn" class="btn btn-outline-secondary">Run Another Test</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Initialize tooltips and recording functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Setup recording functionality for each test suite
    {% for test_suite in test_suites %}
    setupRecorder("{{ test_suite.id }}", {});
    {% endfor %}
});

// Function to open the test runner modal for a specific test suite
function openTestRunnerModal(suiteId) {
    document.getElementById('suiteId').value = suiteId;
    var testRunnerModal = new bootstrap.Modal(document.getElementById('testRunnerModal'));
    testRunnerModal.show();
}
</script>
{% endblock %}