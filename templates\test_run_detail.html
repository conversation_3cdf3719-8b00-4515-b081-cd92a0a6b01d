{% extends 'base.html' %}

{% block title %}{{ test_run.name }} - StreamzAI Test Generator{% endblock %}

{% block header %}Test Run: {{ test_run.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Run Details</h5>
                <div>
                    <a href="{{ url_for('test_suite_detail', suite_id=test_run.test_suite.id) }}" class="btn btn-sm btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Test Suite
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ test_run.name }}</p>
                        <p>
                            <strong>Test Suite:</strong>
                            <a href="{{ url_for('test_suite_detail', suite_id=test_run.test_suite.id) }}">
                                {{ test_run.test_suite.name }}
                            </a>
                        </p>
                        <p>
                            <strong>Project:</strong>
                            <a href="{{ url_for('project_detail', project_id=test_run.test_suite.project.id) }}">
                                {{ test_run.test_suite.project.name }}
                            </a>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Created:</strong> {{ test_run.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p><strong>Start Time:</strong> {{ test_run.start_time.strftime('%Y-%m-%d %H:%M:%S') if test_run.start_time else 'Not started' }}</p>
                        <p><strong>End Time:</strong> {{ test_run.end_time.strftime('%Y-%m-%d %H:%M:%S') if test_run.end_time else 'Not completed' }}</p>
                        <p>
                            <strong>Duration:</strong>
                            {% if test_run.start_time and test_run.end_time %}
                            {{ (test_run.end_time - test_run.start_time).total_seconds()|round(2) }} seconds
                            {% else %}
                            N/A
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Results Summary</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="p-3 bg-light rounded">
                            <h3 class="text-primary">{{ summary.total }}</h3>
                            <p class="mb-0">Total</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 bg-light rounded">
                            <h3 class="text-success">{{ summary.passed }}</h3>
                            <p class="mb-0">Passed</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 bg-light rounded">
                            <h3 class="text-danger">{{ summary.failed }}</h3>
                            <p class="mb-0">Failed</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 bg-light rounded">
                            <h3 class="text-warning">{{ summary.skipped + summary.error }}</h3>
                            <p class="mb-0">Other</p>
                        </div>
                    </div>
                </div>

                {% if summary.total > 0 %}
                <div class="progress mt-3" style="height: 20px;">
                    {% if summary.passed > 0 %}
                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ (summary.passed / summary.total * 100)|round }}%" aria-valuenow="{{ summary.passed }}" aria-valuemin="0" aria-valuemax="{{ summary.total }}">{{ summary.passed }}</div>
                    {% endif %}
                    {% if summary.failed > 0 %}
                    <div class="progress-bar bg-danger" role="progressbar" style="width: {{ (summary.failed / summary.total * 100)|round }}%" aria-valuenow="{{ summary.failed }}" aria-valuemin="0" aria-valuemax="{{ summary.total }}">{{ summary.failed }}</div>
                    {% endif %}
                    {% if summary.skipped > 0 %}
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ (summary.skipped / summary.total * 100)|round }}%" aria-valuenow="{{ summary.skipped }}" aria-valuemin="0" aria-valuemax="{{ summary.total }}">{{ summary.skipped }}</div>
                    {% endif %}
                    {% if summary.error > 0 %}
                    <div class="progress-bar bg-secondary" role="progressbar" style="width: {{ (summary.error / summary.total * 100)|round }}%" aria-valuenow="{{ summary.error }}" aria-valuemin="0" aria-valuemax="{{ summary.total }}">{{ summary.error }}</div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Results Chart</h5>
            </div>
            <div class="card-body text-center">
                {% if chart_path %}
                <img src="{{ url_for('static', filename=chart_path) }}" alt="Test Results Chart" class="img-fluid">
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No chart available.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Results</h5>
            </div>
            <div class="card-body">
                {% if test_results %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Test Case</th>
                                <th>Status</th>
                                <th>Execution Time</th>
                                <th>Error Message</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in test_results %}
                            {% if result.active %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('test_case_detail', case_id=result.test_case.id) }}">
                                        {{ result.test_case.test_file_path }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if result.status == 'passed' else 'danger' if result.status == 'failed' else 'warning' if result.status == 'skipped' else 'secondary' }}">
                                        {{ result.status }}
                                    </span>
                                </td>
                                <td>{{ result.execution_time|round(3) if result.execution_time else 'N/A' }} s</td>
                                <td>
                                    {% if result.error_message %}
                                    <button type="button" class="btn btn-sm btn-link" data-bs-toggle="modal" data-bs-target="#errorModal{{ result.id }}">
                                        View Error
                                    </button>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test results found for this test run.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% for result in test_results %}
{% if result.active and result.error_message %}
<!-- Error Modal -->
<div class="modal fade" id="errorModal{{ result.id }}" tabindex="-1" aria-labelledby="errorModalLabel{{ result.id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorModalLabel{{ result.id }}">Error Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre class="bg-light p-3 rounded"><code>{{ result.error_message }}</code></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}