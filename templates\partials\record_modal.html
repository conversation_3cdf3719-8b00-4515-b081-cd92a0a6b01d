<!-- Record Modal Component -->
<!--
Parameters:
- modal_id: Unique ID for the modal (required)
- test_suite: Test suite object (required)
- element_suffix: Suffix for element IDs (optional, defaults to modal_id)
-->

{% set element_suffix = element_suffix or modal_id %}

<div class="modal fade" id="{{ modal_id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Record Browser Actions for {{ test_suite.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- URL Input Field -->
                <div class="mb-3">
                    <label for="urlInput{{ element_suffix }}" class="form-label">Enter URL to Test</label>
                    <input type="text" id="urlInput{{ element_suffix }}" class="form-control" placeholder="https://example.com">
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Recorded Actions (JSON)</h6>
                        <pre id="recordedActions{{ element_suffix }}" class="bg-light border p-2 rounded" style="height: 300px; overflow: auto; text-align: left;"></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Generated Playwright Code</h6>
                        <pre id="generatedCode{{ element_suffix }}" class="bg-light border p-2 rounded" style="height: 300px; overflow: auto; text-align: left;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex flex-wrap justify-content-center">
                <button class="btn btn-secondary m-1" data-bs-dismiss="modal">Close</button>
                <button class="btn btn-danger m-1" id="stopBtn{{ element_suffix }}">⏹ Stop Recording</button>
                <button class="btn btn-warning m-1" id="clearBtn{{ element_suffix }}">⎌ Clear Actions</button>
                <button class="btn btn-primary m-1" id="startBtn{{ element_suffix }}">🔴 Start Recording</button>
                <button class="btn btn-success m-1" id="saveBtn{{ element_suffix }}">💾 Save Test Case</button>
            </div>
        </div>
    </div>
</div>
