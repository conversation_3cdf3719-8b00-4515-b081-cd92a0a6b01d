"""baseline migration

Revision ID: 5dd7127dd11e
Revises: 
Create Date: 2025-05-27 12:08:23.215674

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5dd7127dd11e'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('path',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=255),
               existing_nullable=False)

    with op.batch_alter_table('test_case', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.TEXT(),
               type_=sa.String(length=100),
               nullable=False,
               existing_server_default=sa.text("'Unnamed Test'"))
        batch_op.drop_column('language')
        batch_op.drop_column('status')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('test_case', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', sa.VARCHAR(length=20), nullable=False))
        batch_op.add_column(sa.Column('language', sa.VARCHAR(length=50), nullable=False))
        batch_op.alter_column('name',
               existing_type=sa.String(length=100),
               type_=sa.TEXT(),
               nullable=True,
               existing_server_default=sa.text("'Unnamed Test'"))

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('path',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=500),
               existing_nullable=False)

    # ### end Alembic commands ###
